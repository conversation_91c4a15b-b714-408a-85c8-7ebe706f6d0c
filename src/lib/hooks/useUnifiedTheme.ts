import { useIsDarkTheme, useThemeActions } from "@/lib/store/selectors";

const lightColors = {
  background: "#F8FAFC",
  surface: "#FFFFFF",
  surfaceSecondary: "#F1F5F9",
  surfaceElevated: "#FFFFFF",

  text: "#1F2937",
  textSecondary: "#6B7280",
  textTertiary: "#9CA3AF",
  textInverse: "#FFFFFF",

  primary: "#14B8A6",
  primaryLight: "#5EEAD4",
  primaryDark: "#0F766E",

  success: "#10B981",
  warning: "#F59E0B",
  error: "#EF4444",
  info: "#3B82F6",

  border: "#E5E7EB",
  borderLight: "#F3F4F6",
  borderFocus: "#14B8A6",

  shadow: "#000000",
  shadowLight: "rgba(0, 0, 0, 0.1)",

  tabBarBackground: "#FFFFFF",
  tabBarBorder: "#E5E7EB",
  tabBarActive: "#14B8A6",
  tabBarInactive: "#6B7280",
};

const darkColors = {
  background: "#0F172A",
  surface: "#1E293B",
  surfaceSecondary: "#334155",
  surfaceElevated: "#475569",

  text: "#F8FAFC",
  textSecondary: "#CBD5E1",
  textTertiary: "#94A3B8",
  textInverse: "#1F2937",

  primary: "#14B8A6",
  primaryLight: "#5EEAD4",
  primaryDark: "#0F766E",

  success: "#10B981",
  warning: "#F59E0B",
  error: "#EF4444",
  info: "#3B82F6",

  border: "#475569",
  borderLight: "#334155",
  borderFocus: "#14B8A6",

  shadow: "#000000",
  shadowLight: "rgba(0, 0, 0, 0.3)",

  tabBarBackground: "#1E293B",
  tabBarBorder: "#475569",
  tabBarActive: "#14B8A6",
  tabBarInactive: "#94A3B8",
};

/**
 * Simple theme hook for the few cases that need programmatic color access
 * Most components should use Tailwind classes instead
 */
export function useTheme() {
  const isDark = useIsDarkTheme();
  const themeActions = useThemeActions();

  // Minimal color set for edge cases that need programmatic access
  const colors = {
    // Primary colors that might be needed for animations or calculations
    primary: "#14B8A6",
    background: isDark ? darkColors.background : lightColors.background,
    text: isDark ? darkColors.text : lightColors.text,
    surface: isDark ? darkColors.surface : lightColors.surface,
    border: isDark ? darkColors.border : lightColors.border,

    // Tab bar colors for navigation
    tabBarBackground: isDark
      ? darkColors.tabBarBackground
      : lightColors.tabBarBackground,
    tabBarBorder: isDark ? darkColors.tabBarBorder : lightColors.tabBarBorder,
    tabBarActive: lightColors.tabBarActive,
    tabBarInactive: isDark
      ? darkColors.tabBarInactive
      : lightColors.tabBarInactive,

    textInverse: isDark ? darkColors.textInverse : lightColors.textInverse,
  };

  return {
    // Theme state
    colors,
    isDark,

    // Theme actions
    ...themeActions,

    // Convenience methods
    toggleTheme: themeActions.toggleTheme,
    setThemeMode: themeActions.setThemeMode,
  };
}

/**
 * Get a theme color programmatically (for edge cases only)
 * Most components should use Tailwind classes instead
 */
export function getThemeColor(colorName: string, isDark: boolean): string {
  const colorMap: Record<string, { light: string; dark: string }> = {
    primary: { light: "#14B8A6", dark: "#14B8A6" },
    background: { light: "#F8FAFC", dark: "#0F172A" },
    text: { light: "#1F2937", dark: "#F8FAFC" },
    surface: { light: "#FFFFFF", dark: "#1E293B" },
    border: { light: "#E5E7EB", dark: "#475569" },
  };

  const color = colorMap[colorName];
  if (!color) {
    console.warn(
      `Unknown color: ${colorName}. Consider using Tailwind classes instead.`,
    );
    return "#000000";
  }

  return isDark ? color.dark : color.light;
}
