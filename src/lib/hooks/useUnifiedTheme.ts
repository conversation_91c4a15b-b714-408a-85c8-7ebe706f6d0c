import { useThemeActions, useThemeColors } from "@/lib/store/selectors";
import { useThemeSync } from "./useThemeSync";

/**
 * Unified theme hook that provides both Zustand colors and ensures synchronization
 * with Tailwind CSS. Use this hook when you need programmatic access to theme colors
 * while still maintaining compatibility with Tailwind classes.
 */
export function useUnifiedTheme() {
  // Ensure theme synchronization
  const { theme, isDark } = useThemeSync();

  // Get theme colors and actions
  const colors = useThemeColors(false);
  const themeActions = useThemeActions();

  return {
    // Theme state
    colors,
    isDark,
    theme,

    // Theme actions
    ...themeActions,

    // Convenience methods
    toggleTheme: themeActions.toggleTheme,
    setThemeMode: themeActions.setThemeMode,
  };
}
