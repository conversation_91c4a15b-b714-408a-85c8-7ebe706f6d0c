import { useEffect } from "react";
import { Platform } from "react-native";
import { useColorScheme } from "@/lib/hooks/useColorScheme";
import { useIsDarkTheme, useTheme } from "@/lib/store/selectors";
import { convertColorsToCSS } from "@/lib/utils";
import { Colors } from "../store/slices/themeSlice";

/**
 * Hook that synchronizes Zustand theme state with CSS custom properties (web)
 * and NativeWind color scheme (React Native)
 */
export function useThemeSync() {
  const theme = useTheme();
  const isDark = useIsDarkTheme();
  const { setColorScheme } = useColorScheme();

  useEffect(() => {
    // Sync with NativeWind color scheme
    setColorScheme(isDark ? "dark" : "light");

    // For web platform, update CSS custom properties
    if (Platform.OS === "web") {
      updateCSSCustomProperties(theme.colors, isDark);
    }
  }, [theme, isDark, setColorScheme]);

  return { theme, isDark };
}

/**
 * Updates CSS custom properties with current theme colors (web only)
 */
function updateCSSCustomProperties(
  colors: Colors,
  isDark: boolean,
) {
  if (typeof document === "undefined") return;

  const root = document.documentElement;
  const cssVars = convertColorsToCSS(colors);

  // Apply the CSS custom properties
  Object.entries(cssVars).forEach(([property, value]) => {
    root.style.setProperty(property, value);
  });

  // Ensure the dark class is applied/removed correctly
  if (isDark) {
    root.classList.add("dark");
  } else {
    root.classList.remove("dark");
  }
}

/**
 * Initialize theme synchronization - call this once in your app root
 */
export function initializeThemeSync() {
  if (Platform.OS === "web" && typeof document !== "undefined") {
    // Ensure the document has the proper classes for Tailwind
    document.documentElement.classList.add("bg-background");
  }
}
