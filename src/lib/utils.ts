import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";
import { Colors } from "./store/slices/themeSlice";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Platform-aware class merging utility.
 * Handles React Native web styling by removing web-only classes on native platforms.
 */
export function platformCn(...inputs: ClassValue[]) {
  // eslint-disable-next-line @typescript-eslint/no-require-imports
  const Platform = require("react-native").Platform;

  if (Platform.OS === "web") {
    return cn(...inputs);
  }

  // On native platforms, filter out web-specific classes
  const filtered = inputs.map((input) => {
    if (typeof input === "string") {
      return input
        .split(" ")
        .filter((cls) => !cls.startsWith("web:"))
        .join(" ");
    }
    return input;
  });

  return cn(...filtered);
}

/**
 * Utility to create responsive values for React Native
 * @param base Base value for all platforms
 * @param native Native-specific value
 * @param web Web-specific value
 */
export function createResponsiveValue<T>(
  base: T,
  native?: T,
  web?: T,
): T {
  // eslint-disable-next-line @typescript-eslint/no-require-imports
  const Platform = require("react-native").Platform;

  if (Platform.OS === "web" && web !== undefined) {
    return web;
  }

  if (Platform.OS !== "web" && native !== undefined) {
    return native;
  }

  return base;
}

/**
 * Convert hex color to HSL format for CSS custom properties
 */
export function hexToHsl(hex: string): string {
  // Remove the hash if present
  hex = hex.replace("#", "");

  // Parse RGB values
  const r = parseInt(hex.substring(0, 2), 16) / 255;
  const g = parseInt(hex.substring(2, 4), 16) / 255;
  const b = parseInt(hex.substring(4, 6), 16) / 255;

  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  let h = 0;
  let s = 0;
  const l = (max + min) / 2;

  if (max !== min) {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

    switch (max) {
      case r:
        h = (g - b) / d + (g < b ? 6 : 0);
        break;
      case g:
        h = (b - r) / d + 2;
        break;
      case b:
        h = (r - g) / d + 4;
        break;
    }
    h /= 6;
  }

  // Convert to degrees and percentages
  const hDeg = Math.round(h * 360);
  const sPercent = Math.round(s * 100);
  const lPercent = Math.round(l * 100);

  return `${hDeg} ${sPercent}% ${lPercent}%`;
}

/**
 * Mapping between Zustand color names and CSS custom property names
 */
export const COLOR_MAPPING = {
  // Background colors
  background: "--background",
  surface: "--card",
  surfaceSecondary: "--muted",
  surfaceElevated: "--card",

  // Text colors
  text: "--foreground",
  textSecondary: "--muted-foreground",
  textTertiary: "--muted-foreground",
  textInverse: "--primary-foreground",

  // Primary colors
  primary: "--primary",
  primaryLight: "--primary",
  primaryDark: "--primary",

  // Status colors
  success: "--success",
  warning: "--warning",
  error: "--destructive",
  info: "--info",

  // Border colors
  border: "--border",
  borderLight: "--border",
  borderFocus: "--ring",

  // Additional mappings for completeness
  shadow: "--shadow",
  shadowLight: "--shadow-light",
  tabBarBackground: "--card",
  tabBarBorder: "--border",
  tabBarActive: "--primary",
  tabBarInactive: "--muted-foreground",
} as const;

/**
 * Convert Zustand colors object to CSS custom properties format
 */
export function convertColorsToCSS(
  colors: Colors,
): Record<string, string> {
  const cssVars: Record<string, string> = {};

  Object.entries(colors).forEach(([key, value]) => {
    const cssVarName = COLOR_MAPPING[key as keyof typeof COLOR_MAPPING];
    if (cssVarName && value) {
      cssVars[cssVarName] = hexToHsl(value);
    }
  });

  return cssVars;
}

/**
 * Safely access theme colors with fallbacks
 */
export function getThemeColor(
  colorVar: string,
  fallback: string = "#000000",
): string {
  // For React Native, we'll need to use our theme system
  // This will be integrated with the CSS variables system
  return fallback;
}
