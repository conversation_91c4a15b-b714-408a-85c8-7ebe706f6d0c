import React from 'react';
import { View, Text, Pressable } from 'react-native';
import { useTheme } from '@/lib/hooks/useUnifiedTheme';

/**
 * Test component to verify the simplified theme system works correctly
 */
export function ThemeTestComponent() {
  const { isDark, toggleTheme, setThemeMode, colors } = useTheme();

  return (
    <View className="flex-1 p-4 bg-background">
      <Text className="text-2xl font-bold text-foreground mb-4">
        Theme Test Component
      </Text>
      
      <Text className="text-foreground mb-2">
        Current theme: {isDark ? 'Dark' : 'Light'}
      </Text>
      
      <Text className="text-muted-foreground mb-4">
        This component tests both Tailwind classes and programmatic color access
      </Text>

      {/* Test Tailwind classes */}
      <View className="mb-6">
        <Text className="text-lg font-semibold text-foreground mb-2">
          Tailwind Classes Test:
        </Text>
        
        <View className="p-4 bg-card border border-border rounded-lg mb-2">
          <Text className="text-card-foreground">Card with border</Text>
        </View>
        
        <View className="p-4 bg-muted rounded-lg mb-2">
          <Text className="text-muted-foreground">Muted background</Text>
        </View>
        
        <View className="p-2 bg-primary rounded">
          <Text className="text-primary-foreground text-center">Primary button style</Text>
        </View>
      </View>

      {/* Test programmatic colors */}
      <View className="mb-6">
        <Text className="text-lg font-semibold text-foreground mb-2">
          Programmatic Colors Test:
        </Text>
        
        <View 
          style={{ 
            padding: 16, 
            backgroundColor: colors.surface, 
            borderColor: colors.border,
            borderWidth: 1,
            borderRadius: 8,
            marginBottom: 8
          }}
        >
          <Text style={{ color: colors.text }}>
            Surface with programmatic colors
          </Text>
        </View>
        
        <View 
          style={{ 
            padding: 12, 
            backgroundColor: colors.primary, 
            borderRadius: 6 
          }}
        >
          <Text style={{ color: '#FFFFFF', textAlign: 'center' }}>
            Primary background
          </Text>
        </View>
      </View>

      {/* Theme controls */}
      <View>
        <Text className="text-lg font-semibold text-foreground mb-2">
          Theme Controls:
        </Text>
        
        <View className="flex-row gap-2 mb-2">
          <Pressable
            onPress={toggleTheme}
            className="flex-1 p-3 bg-secondary rounded-lg"
          >
            <Text className="text-secondary-foreground text-center">
              Toggle Theme
            </Text>
          </Pressable>
        </View>
        
        <View className="flex-row gap-2">
          <Pressable
            onPress={() => setThemeMode('light')}
            className="flex-1 p-2 bg-card border border-border rounded"
          >
            <Text className="text-card-foreground text-center text-sm">
              Light
            </Text>
          </Pressable>
          
          <Pressable
            onPress={() => setThemeMode('dark')}
            className="flex-1 p-2 bg-card border border-border rounded"
          >
            <Text className="text-card-foreground text-center text-sm">
              Dark
            </Text>
          </Pressable>
          
          <Pressable
            onPress={() => setThemeMode('system')}
            className="flex-1 p-2 bg-card border border-border rounded"
          >
            <Text className="text-card-foreground text-center text-sm">
              System
            </Text>
          </Pressable>
        </View>
      </View>
    </View>
  );
}
