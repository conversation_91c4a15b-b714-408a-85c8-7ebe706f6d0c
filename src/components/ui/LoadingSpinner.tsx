import React, { useEffect } from 'react';
import { StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withRepeat,
  withTiming,
  withSequence,
  interpolate,
  Easing,
} from 'react-native-reanimated';
import { useUnifiedTheme } from '@/lib/hooks/useUnifiedTheme';

interface LoadingSpinnerProps {
  readonly size?: number;
  readonly isVisible: boolean;
  readonly color?: string;
}

export default function LoadingSpinner({
  size = 48,
  isVisible,
  color,
}: LoadingSpinnerProps) {
  const { colors } = useUnifiedTheme();
  const rotation = useSharedValue(0);
  const scale = useSharedValue(0);
  const opacity = useSharedValue(0);

  const spinnerColor = color ?? colors.primary;

  useEffect(() => {
    if (isVisible) {
      // Entrance animation
      opacity.value = withTiming(1, { duration: 300 });
      scale.value = withSequence(
        withTiming(1.2, {
          duration: 200,
          easing: Easing.out(Easing.back(1.5)),
        }),
        withTiming(1, { duration: 100 })
      );

      // Continuous rotation with scale pulse
      rotation.value = withRepeat(
        withTiming(360, {
          duration: 1500,
          easing: Easing.linear,
        }),
        -1
      );
    } else {
      // Exit animation
      opacity.value = withTiming(0, { duration: 200 });
      scale.value = withTiming(0.8, { duration: 200 });
    }
  }, [isVisible, opacity, rotation, scale]);

  const containerStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
    transform: [{ scale: scale.value }],
  }));

  const spinnerStyle = useAnimatedStyle(() => {
    const rotationDegrees = `${rotation.value}deg`;
    const pulseScale = interpolate(
      rotation.value % 360,
      [0, 180, 360],
      [1, 1.05, 1]
    );

    return {
      transform: [{ rotate: rotationDegrees }, { scale: pulseScale }],
    };
  });

  const innerRingStyle = useAnimatedStyle(() => {
    const innerRotation = `${-rotation.value * 0.7}deg`;
    return {
      transform: [{ rotate: innerRotation }],
    };
  });

  if (!isVisible && opacity.value === 0) return null;

  return (
    <Animated.View style={[styles.container, containerStyle]}>
      <Animated.View
        style={[
          styles.spinner,
          {
            width: size,
            height: size,
            borderRadius: size / 2,
            borderWidth: size / 12,
            borderColor: `${spinnerColor}20`,
            borderTopColor: spinnerColor,
          },
          spinnerStyle,
        ]}
      >
        <Animated.View
          style={[
            styles.innerRing,
            {
              width: size * 0.6,
              height: size * 0.6,
              borderRadius: (size * 0.6) / 2,
              borderWidth: size / 16,
              borderColor: 'transparent',
              borderRightColor: `${spinnerColor}60`,
            },
            innerRingStyle,
          ]}
        />
      </Animated.View>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  spinner: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  innerRing: {
    position: 'absolute',
  },
});
