import React from 'react';
import { View } from 'react-native';
import { Button } from '@/components/ui/button';
import { Text } from '@/components/ui/text';
import { Card } from '@/components/ui/card';
import { useTheme } from '@/lib/hooks/useUnifiedTheme';

/**
 * Test component to verify theme switching functionality
 * This component demonstrates both Tailwind classes and programmatic color access
 */
export function ThemeTestComponent() {
  const { colors, isDark, toggleTheme, setThemeMode } = useTheme();

  return (
    <View className="p-4 bg-background">
      <Card className="p-6 mb-4">
        <Text className="text-2xl font-bold text-foreground mb-4">
          Theme Test Component
        </Text>

        <Text className="text-base text-muted-foreground mb-4">
          Current theme: {isDark ? 'Dark' : 'Light'}
        </Text>

        {/* Tailwind-based styling */}
        <View className="mb-4">
          <Text className="text-lg font-semibold text-foreground mb-2">
            Tailwind Classes:
          </Text>
          <View className="bg-card border border-border rounded-lg p-3 mb-2">
            <Text className="text-card-foreground">Card with border</Text>
          </View>
          <View className="bg-primary rounded-lg p-3 mb-2">
            <Text className="text-primary-foreground">Primary background</Text>
          </View>
          <View className="bg-secondary rounded-lg p-3 mb-2">
            <Text className="text-secondary-foreground">
              Secondary background
            </Text>
          </View>
        </View>

        {/* Programmatic color access */}
        <View className="mb-4">
          <Text className="text-lg font-semibold text-foreground mb-2">
            Programmatic Colors:
          </Text>
          <View
            style={{
              backgroundColor: colors.surface,
              borderColor: colors.border,
              borderWidth: 1,
              borderRadius: 8,
              padding: 12,
              marginBottom: 8,
            }}
          >
            <Text style={{ color: colors.text }}>
              Surface: {colors.surface}
            </Text>
          </View>
          <View
            style={{
              backgroundColor: colors.primary,
              borderRadius: 8,
              padding: 12,
              marginBottom: 8,
            }}
          >
            <Text style={{ color: colors.textInverse }}>
              Primary: {colors.primary}
            </Text>
          </View>
        </View>

        {/* Theme controls */}
        <View className="flex-row gap-2 mb-4">
          <Button onPress={toggleTheme} className="flex-1">
            <Text>Toggle Theme</Text>
          </Button>
        </View>

        <View className="flex-row gap-2">
          <Button
            variant="outline"
            onPress={() => setThemeMode('light')}
            className="flex-1"
          >
            <Text>Light</Text>
          </Button>
          <Button
            variant="outline"
            onPress={() => setThemeMode('dark')}
            className="flex-1"
          >
            <Text>Dark</Text>
          </Button>
          <Button
            variant="outline"
            onPress={() => setThemeMode('system')}
            className="flex-1"
          >
            <Text>System</Text>
          </Button>
        </View>
      </Card>
    </View>
  );
}
